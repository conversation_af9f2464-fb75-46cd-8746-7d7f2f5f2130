const { defineConfig } = require("@vue/cli-service");
module.exports = defineConfig({
    runtimeCompiler: true,
    transpileDependencies: true,
    lintOnSave: false,
    publicPath: process.env.VUE_APP_FILE || "./",
    outputDir: "build",
    productionSourceMap: false,
    chainWebpack: config => {
        config.module
            .rule("images")
            .test(/\.(jpg|jpeg|png|gif)$/)
            .set("parser", {
                dataUrlCondition: {
                    maxSize: 2 * 1024, // 10KiB
                },
            });
    },

    devServer: {
        proxy: {
            '/activity-app-api': {
                target: 'https://jsbceshi.hfi-health.com:18188',//测试环境
                changeOrigin: true,
                // logLevel: 'debug',
                ws: true,
                secure: false,
                pathRewrite: {
                    '^/activity-app-api': '/activity-app-api'
                }
            },
            '/api': {
                target: 'http://************:18188',//测试环境
                changeOrigin: true,
                ws: true,
                secure: false,
                pathRewrite: {
                    '^/api': '/api'
                }
            },
            '/health_mall': {
                target: 'https://jsbceshi.hfi-health.com:18188/health_mall',
                changeOrigin: true,
                pathRewrite: {
                    "^/health_mall": "",
                },
            },
            "/wechat-java/": {
                // target:'https://jsbceshi.hfi-health.com:18188/wechat-java/',
                target: "https://www.hfi-health.com:28181/wechat-java/",
                // target:'http://***************:18181/hzAppMS/',
                pathRewrite: {
                    "^/wechat-java/": "",
                },
            },
            "/hzAppMS": {
                target: "https://jsbceshi.hfi-health.com:18188/hzAppMS", //测试环境
                // target: "http://************:18188/hzAppMS", //测试环境ip
                // target: "https://www.hfi-health.com:28181/hzAppMS",
                changeOrigin: true,
                pathRewrite: {
                    "^/hzAppMS": "", // 实际请求去掉/hzAppMS以空字符串代替
                },
            },
            "/stageBak": {
                target: "https://jsbceshi.hfi-health.com:18188/stageBak",
                // target: 'https://www.hfi-health.com:28181',
                changeOrigin: true,
                pathRewrite: {
                    "^/stageBak": "", // 实际请求去掉/hzAppMS以空字符串代替
                },
            },
            "/inthos": {
                // target: "http://*************:29011/inthos",
                target: "https://jsbceshi.hfi-health.com:18188/inthos",
                // target: 'https://www.hfi-health.com:28181/inthos',
                changeOrigin: true,
                pathRewrite: {
                    "^/inthos": "", // 实际请求去掉/hzAppMS以空字符串代替
                },
            },
            "/hdcp": {
                target: "https://jsbceshi.hfi-health.com:18188/hdcp", //测试环境
                // target: "http://************:18188/hzAppMS", //测试环境ip
                // target: "https://www.hfi-health.com:28181/hzAppMS",
                changeOrigin: true,
                pathRewrite: {
                    "^/hdcp": "", // 实际请求去掉/hzAppMS以空字符串代替
                },
            },
        },
    },
    // chainWebpack: () => {}
    /* chainWebpack: config => {
            devServer: {
                proxy: {
                  '/hzAppMS': {
                    // target: 'https://jsbceshi.hfi-health.com:18188/hzAppMS', //测试环境
                    // target: 'http://************:18188/hzAppMS',  //测试环境ip
                    target: 'https://www.hfi-health.com:28181/hzAppMS',
                    changeOrigin: true,
                    pathRewrite: {
                      '^/hzAppMS': '' // 实际请求去掉/hzAppMS以空字符串代替
                    },
                  },
                  '/stageBak':{
                    target: 'https://jsbceshi.hfi-health.com:18188',
                    // target: 'https://www.hfi-health.com:28181',
                    changeOrigin: true,
                  }
                }
              },
            // chainWebpack: () => {}
            /* chainWebpack: config => {
                console.log(config)
                config.module
                    .rule('images')
                    .use('url-loader')
                    .loader('url-loader')
                    .tap(options => { 
                        console.log(options)
                        return Object.assign(options||{}, { limit: 2 }) 
                    })
            } */
});
