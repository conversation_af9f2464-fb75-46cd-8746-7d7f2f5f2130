/*
 * @Author: shenpp
 * @Date: 2023-05-22
 * @LastEditTime: 2025-04-08 18:38:16
 * @Description:
 */
import { Dialog, Toast } from "vant";
import cryptoJS from "crypto-js";
import JSEncrypt from 'jsencrypt';
import { encryptSwitch } from "@/api/my";
const util = {
    getENMode() {
        return new Promise((r, j) => {
            if (!window.sessionStorage.getItem('hyejEncryptSwitch')) {
                encryptSwitch().then((res) => {
                    if (res && res.code == 200 && res.data) {
                        window.sessionStorage.setItem('hyejEncryptSwitch', res.data.encryptSwitch ? 'yes' : 'no')
                        window.sessionStorage.setItem('rsaKey', res.data.publicKey || '')
                        console.log(3333333)
                        r(true)
                    } else {
                        console.log(2222222222)
                        r(false)
                    }
                })
            }
        })
    },
    rsa(message) {
        // const JSEncrypt = require('jsencrypt').default;
        const jsEncrypt = new JSEncrypt();
        let key = window.sessionStorage.getItem('rsaKey')
        // 设置公钥
        jsEncrypt.setPublicKey(key);
        // 使用公钥加密
        const encrypted = jsEncrypt.encrypt(message);
        console.log('Encrypted:', encrypted);
        return encrypted;
        // console.log('Encrypted:', Base64.encode(encrypted));
    },

    getKEY(url) {
        let key = window.sessionStorage.getItem(url);

        // todo 是否需要清楚掉缓存
        window.sessionStorage.removeItem(url);
        return key;
    },
    removeKey(url) {
        window.sessionStorage.removeItem(url);
    },

    getAesKey(url) {
        let key = cryptoJS.lib.WordArray.random(16).toString(cryptoJS.enc.Hex)
        window.sessionStorage.setItem(url, key)
        return key;
        // CryptoJS.lib.WordArray.random(256 / 8).toString(CryptoJS.enc.Hex); // 使用Hex格式表示密钥
    },

    // 与后台交互的加密
    encryptAES(aesKey, content) {
        let sKey = cryptoJS.enc.Utf8.parse(aesKey);
        let sContent = cryptoJS.enc.Utf8.parse(content);
        let encrypted = cryptoJS.AES.encrypt(sContent, sKey, {
            mode: cryptoJS.mode.ECB,
            padding: cryptoJS.pad.Pkcs7,
        });
        // return encrypted.ciphertext.toString()
        return encrypted.toString();
    },
    // 后台交互解密
    dencryptAES(aesKey, content) {
        var key = cryptoJS.enc.Utf8.parse(aesKey);
        // var datahex = cryptoJS.enc.Hex.parse(content);
        // var dataBase64 = cryptoJS.enc.Base64.stringify(datahex);
        var decrypted = cryptoJS.AES.decrypt(content.toString(), key, {
            mode: cryptoJS.mode.ECB,
            padding: cryptoJS.pad.Pkcs7,
        });
        // console.log(decrypted,'00',dataBase64)
        return decrypted.toString(cryptoJS.enc.Utf8);
    },
    isInMini() {
        debugger
        return window.sessionStorage.getItem('inMini') == 'in'
    },
    // 跳转区分是否在微信小程序
    back(url) {
        if (this.isInMini()) {
            wx.miniProgram.navigateBack();
        } else {
            // 
            window.location.replace(url)
        }

    },
    // 跳转区分是否在微信小程序
    jump(url, isreplace) {
        debugger
        if (this.isInMini()) {
            if (url.indexOf('http://') > -1 || url.indexOf('https://') > -1) {

            } else {
                url = location.origin + location.pathname + url
            }
            wx.miniProgram.navigateTo({
                url: "/pages/second/second?rurl=" + encodeURIComponent(url) + "&curl=" + encodeURIComponent(location.href),
            });
        } else {
            window.location.href = url
        }

    },
    // 退登
    loginOut(isSecond) {
        if (isSecond) {
            this.showToast("登录失效啦，即将重新登录~", 1500);
        }
        window.localStorage.removeItem("access_token")
        window.localStorage.removeItem("userInfo")

        // 保存当前路由路径，而不是完整URL
        const currentPath = window.location.hash || '#/';
        window.sessionStorage.setItem("acURL", currentPath)

        this.jump("#/login")
    },
    // 获取传参
    getQueryStringHash(name) {
        let result = "";
        if (window.location.hash.includes("?")) {
            window.location.hash
                .split("?")[1]
                .split("&")
                .forEach((val) => {
                    if (val.includes(name)) {
                        result = val.substring(name.length + 1);
                    }
                });
        }
        // console.log('result', result)
        return result;
    },
    showToast(
        message = "网络连接超时，请稍后再试~",
        duration = 1500,
        forbidClick = true
    ) {
        Toast({
            duration,
            forbidClick,
            message,
        });
    },
    showLoading({
        message = "努力加载中，请稍后",
        forbidClick = true,
        loadingType = "spinner",
        duration = 0,
    } = {}) {
        Toast.loading({
            message,
            forbidClick,
            loadingType,
            duration,
        });
    },
    hideLoading() {
        Toast.clear();
    },
    openDialogAlert(title, content, fun, text, className, overlayClass) {
        return Dialog.alert({
            title,
            className,
            overlayClass,
            message: content,
            confirmButtonText: text || "确认",
            confirmButtonColor: "#07C160",
        })
            .then(() => {
                if (fun) {
                    Dialog.close();
                    fun();
                }
            })
            .catch(() => {
                // this.showToast('取消')
            });
    },
    generateUUID() {
        var d = new Date().getTime();
        if (window.performance && typeof window.performance.now === "function") {
            d += performance.now(); //use high-precision timer if available
        }
        var uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
            /[xy]/g,
            function (c) {
                var r = (d + Math.random() * 16) % 16 | 0;
                d = Math.floor(d / 16);
                return (c == "x" ? r : (r & 0x3) | 0x8).toString(16);
            }
        );
        return uuid;
    },
    // loginOut() {
    //     console.log("退出登录");

    //     // 退出登录
    //     window.localStorage.clear();
    //     window.sessionStorage.clear();
    //     if (
    //         navigator.userAgent.indexOf("AliApp") > -1 &&
    //         window.localStorage.getItem("interHosp_origin") != "szMini"
    //     ) {
    //         my.postMessage({
    //             action: "clearStorage",
    //         });
    //         my.navigateTo({
    //             url: "/pages/index/index",
    //         });
    //         // window.location.replace("#/home");
    //         return;
    //     }
    //     if (navigator.userAgent.toLowerCase().indexOf("toutiaomicroapp") > -1) {
    //         // 清除后，需跳转到小程序页面，小程序页面缓存也要清除
    //         // let setTime = setInterval(() => {
    //         //     console.log("跳转到首页");
    //         //     clearInterval(setTime);
    //         //     tt.miniProgram.navigateTo({
    //         //         url: "/pages/wxLogOut/wxLogOut",
    //         //     });
    //         // }, 500);
    //         return
    //     }

    //     // 清除后，需跳转到小程序页面，小程序页面缓存也要清除
    //     let setTime = setInterval(() => {
    //         console.log("跳转到首页");
    //         clearInterval(setTime);
    //         wx.miniProgram.navigateTo({
    //             url: "/pages/wxLogOut/wxLogOut",
    //         });
    //     }, 500);
    // }
};

export default util;
