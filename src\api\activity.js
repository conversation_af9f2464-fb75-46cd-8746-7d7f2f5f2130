/**
 * @Author: Activity H5 Project
 * @Date: 2025-01-01
 * @Description: 活动相关API接口
 */

import request, { get } from '@/utils/request';



// 获取Banner轮播图 - 使用新的独立接口
export const getBannerList = () => {
  return request({
    url: '/app/activity/getBanner',
    method: 'post',
    data: {}
  }).catch(error => {
    console.error('getBannerList接口调用失败:', error);
    throw error;
  });
};

// 获取精选活动 - 使用新的独立接口
export const getFeaturedActivityList = () => {
  return request({
    url: '/app/activity/getLocation',
    method: 'post',
    data: {}
  }).catch(error => {
    console.error('getFeaturedActivityList接口调用失败:', error);
    throw error;
  });
};



// 获取活动类型分类
export const getActivityCategoryList = (params = {}, options = {}) => {
  return request({
    url: '/app/activity/getTypeList',
    method: 'post',
    data: {
      delFlag: '0',
      ...params
    },
    ...options
  }).catch(error => {
    console.error('getActivityCategoryList接口调用失败:', error);
    throw error;
  });
};

// 获取活动列表（按类型）
export const getActivityList = (params = {}) => {
  return request({
    url: '/app/activity/getByTypeId',
    method: 'post',
    data: {
      typeId: null,
      popularity: 0,
      selectSort: 0,
      ...params
    }
  });
};

// 按类型获取活动列表（getByTypeId接口的别名）
export const getByTypeId = (params = {}) => {
  return getActivityList(params);
};

// 活动详情
export const getActivityDetail = (actId) => {
  return get('/app/activity/detail', { actId });
};

// 筛选活动列表
export const chooseActivity = (params = {}) => {
  return request({
    url: '/app/activity/chooseAct',
    method: 'post',
    data: {
      typeId: null,
      activityStatus: null,
      registerStatus: null,
      ...params
    }
  });
};

// 搜索活动
export const searchActivity = (params = {}, disableGlobalLoading = false) => {
  return request({
    url: '/app/activity/searchAct',
    method: 'post',
    data: {
      actId: null,
      actTitle: null,
      ...params
    },
    disableGlobalLoading
  });
};

// 一键报名活动
export const registerActivity = (data, showSelfFail = false) => {
  return request({
    url: '/app/register/actInfo',
    method: 'post',
    data: {
      id: null,
      name: null,
      phone: null,
      cardType: null,
      idCard: null,
      gender: null,
      age: null,
      human: null,
      child: null,
      high: null,
      weight: null,
      educate: null,
      community: null,
      address: null,
      selfAdds: null,
      ...data
    },
    showSelfFail
  });
};

// 获取报名详情
export const getRegisterDetail = (id) => {
  return get('/app/register/registerDetailById', { id });
};

// 取消报名s
export const cancelRegister = (id) => {
  return get('/app/register/cannelRegister', { id });
};

