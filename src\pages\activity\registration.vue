<template>
  <div class="registration-page">
    <!-- 导航栏 -->
    <!-- <van-nav-bar
      title="活动报名"
      left-arrow
      @click-left="goBack"
    /> -->

    <!-- 活动信息部分 -->
    <div class="activity-info-section">
      <div class="section-header">
        <div class="header-left">
          <div class="orange-bar"></div>
          <span class="section-title">活动信息</span>
        </div>
      </div>
      
      <!-- 收起状态：只显示前2条关键信息 -->
      <div class="activity-info-content" v-show="!isActivityInfoExpanded">
        <div class="info-item">
          <span class="label">活动名称：</span>
          <span class="value">{{ activity.actTitle || 'XXXXXXXXXXXXXXXXXXXXXX' }}</span>
        </div>
        <div class="info-item">
          <span class="label">活动时间：</span>
          <span class="value">{{ activity.actTime || '时间待定' }}</span>
        </div>
        <div class="expand-trigger" @click="toggleActivityInfo">
          <van-icon 
            :name="isActivityInfoExpanded ? 'arrow-up' : 'arrow-down'" 
            class="expand-icon"
          />
        </div>
      </div>
      
      <!-- 展开状态：显示所有信息 -->
      <div class="activity-info-content" v-show="isActivityInfoExpanded">
        <div class="info-item">
          <span class="label">活动名称：</span>
          <span class="value">{{ activity.actTitle || 'XXXXXXXXXXXXXXXXXXXXXX' }}</span>
        </div>
        <div class="info-item">
          <span class="label">活动时间：</span>
          <span class="value">{{ activity.actTime || '时间待定' }}</span>
        </div>
        <div class="info-item">
          <span class="label">活动地址：</span>
          <span class="value">{{ activity.locationString || '地址待定' }}</span>
        </div>
        <div class="info-item">
          <span class="label">活动须知：</span>
          <span class="value">{{ activity.actNotice || '暂无须知' }}</span>
        </div>
        <!-- <div class="info-item" v-if="activity.actDesc">
          <span class="label">活动描述：</span>
          <span class="value">{{ activity.actDesc }}</span>
        </div> -->
        <div class="expand-trigger" @click="toggleActivityInfo">
          <van-icon 
            :name="isActivityInfoExpanded ? 'arrow-up' : 'arrow-down'" 
            class="expand-icon"
          />
        </div>
      </div>
    </div>

    <!-- 报名信息部分 -->
    <div class="registration-form-section">
      <div class="section-header">
        <div class="header-left">
          <div class="orange-bar"></div>
          <span class="section-title">请填写下列报名信息</span>
        </div>
      </div>

      <div class="registration-form">
        <!-- 动态字段 -->
        <div class="form-group">
          <div 
            v-for="field in allFields" 
            :key="field.id"
            class="dynamic-field"
          >
            <!-- 身高字段特殊处理 -->
            <van-field
              v-if="field.type === 'text' && field.id === 'height'"
              v-model="formData[field.id]"
              :placeholder="`请输入${field.label}`"
              type="number"
              inputmode="decimal"
              :error-message="fieldErrors[field.id]"
              @input="limitDecimalPlaces(field.id, 2)"
              @blur="formatDecimalPlaces(field.id, 2); validateField(field.id)"
            >
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
              <template #right-icon>
                <span class="unit">cm</span>
              </template>
            </van-field>

            <!-- 体重字段特殊处理 -->
            <van-field
              v-else-if="field.type === 'text' && field.id === 'weight'"
              v-model="formData[field.id]"
              :placeholder="`请输入${field.label}`"
              type="number"
              inputmode="decimal"
              :error-message="fieldErrors[field.id]"
              @input="limitDecimalPlaces(field.id, 2)"
              @blur="formatDecimalPlaces(field.id, 2); validateField(field.id)"
            >
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
              <template #right-icon>
                <span class="unit">kg</span>
              </template>
            </van-field>

            <!-- 年龄字段特殊处理 -->
            <van-field
              v-else-if="field.type === 'text' && field.id === 'age'"
              v-model="formData[field.id]"
              :placeholder="`请输入${field.label}`"
              type="number"
              inputmode="numeric"
              :error-message="fieldErrors[field.id]"
              @input="limitToInteger(field.id)"
              @blur="validateField(field.id)"
            >
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
            </van-field>

            <!-- 携带亲属字段特殊处理 - 双步进器 -->
            <van-field
              v-else-if="field.id === 'relative' || field.label.includes('亲属') || field.label.includes('携带')"
              :label="field.label"
              :error-message="fieldErrors[field.id]"
            >
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
              <template #input>
                <div class="stepper-group">
                  <div class="stepper-item">
                    <van-stepper
                      v-model="formData[field.id + '_adult']"
                      :max="field.human || maxFamilyMembers"
                      :min="0"
                      @change="onRelativeStepperChange(field)"
                    />
                    <span class="stepper-label">大</span>
                  </div>
                  <div class="stepper-item">
                    <van-stepper
                      v-model="formData[field.id + '_child']"
                      :max="field.child || maxFamilyMembers"
                      :min="0"
                      @change="onRelativeStepperChange(field)"
                    />
                    <span class="stepper-label">小</span>
                  </div>
                </div>
              </template>
            </van-field>

            <!-- 性别特殊处理 - 单选按钮 -->
            <van-field v-else-if="field.id === 'gender'" :error-message="fieldErrors.gender">
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
              <template #input>
                <div class="gender-options">
                  <label class="radio-option" v-for="option in field.options" :key="option">
                    <input 
                      type="radio" 
                      :value="option" 
                      v-model="formData.gender"
                      @change="validateField('gender')"
                    />
                    <span class="radio-label">{{ option }}</span>
                  </label>
                </div>
              </template>
            </van-field>
            
            <!-- 身份证号字段特殊处理 -->
            <van-field
              v-else-if="field.type === 'text' && (field.isIdCard || field.id === 'idCard')"
              :label="field.label"
              :error-message="fieldErrors[field.id]"
              clickable
              @click="openIdKeyboard(field.id)"
            >
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
              <template #input>
                <div class="idcard-input-wrapper">
                  <input
                    v-model="formData[field.id]"
                    :placeholder="`请输入${field.label}`"
                    type="text"
                    inputmode="none"
                    pattern="[0-9Xx]*"
                    maxlength="18"
                    class="idcard-number-input"
                    readonly
                    tabindex="0"
                    @click="openIdKeyboard(field.id)"
                    @blur="validateField(field.id)"
                  />
                </div>
              </template>
            </van-field>

              <!-- 普通文本输入框 -->
              <van-field
              v-else-if="field.type === 'text' && !(field.isIdCard || field.id === 'idCard')"
              v-model="formData[field.id]"
              :placeholder="`请输入${field.label}`"
              :error-message="fieldErrors[field.id]"
              @click="closeIdKeyboard"
              @blur="validateField(field.id)"
            >
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
            </van-field>
            
            <!-- 多行文本框 -->
            <van-field
              v-else-if="field.type === 'textarea'"
              v-model="formData[field.id]"
              :placeholder="`请输入${field.label}`"
              type="textarea"
              rows="3"
              :error-message="fieldErrors[field.id]"
              @click="closeIdKeyboard"
              @blur="validateField(field.id)"
            >
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
            </van-field>

            <!-- 人数相关字段特殊处理 - 单步进器 -->
            <van-field
              v-else-if="field.type === 'text' && (field.id === 'adultCount' || field.id === 'childCount' || field.id.includes('Count') || field.label.includes('人数'))"
              :label="field.label"
              :error-message="fieldErrors[field.id]"
            >
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
              <template #input>
                <van-stepper
                  v-model="formData[field.id]"
                  :max="maxFamilyMembers"
                  :min="0"
                  @change="onStepperChange(field.id)"
                />
              </template>
            </van-field>

            <!-- 选择器 -->
            <van-field
              v-else-if="field.type === 'select'"
              v-model="formData[field.id]"
              :placeholder="`请选择${field.label}`"
              readonly
              is-link
              @click="showFieldPicker(field)"
              :error-message="fieldErrors[field.id]"
            >
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
            </van-field>
          </div>
        </div>

      </div>
    </div>

    <!-- 提交按钮 -->
    <div class="submit-section">
      <van-button
        type="primary"
        size="large"
        :loading="submitting"
        :disabled="submitting"
        @click="submitRegistration"
        class="submit-button"
      >
        {{ submitting ? '提交中...' : '提交报名' }}
      </van-button>
    </div>

    <!-- 选择器弹窗 -->


    <!-- 自定义字段选择器 -->
    <van-popup
      v-model="showCustomFieldPickerDialog"
      position="bottom"
      round
      :close-on-click-overlay="true"
    >
      <van-picker
        :columns="currentCustomFieldOptions"
        :title="currentCustomField ? `请选择${currentCustomField.label}` : '请选择'"
        show-toolbar
        @confirm="onCustomFieldConfirm"
        @cancel="onCustomFieldCancel"
      />
    </van-popup>

    <!-- 成功提示弹窗 -->
    <van-dialog
      v-model="showSuccessDialog"
      title="温馨提示"
      class="success-dialog"
      :show-confirm-button="false"
      :show-cancel-button="false"
    >
      <div class="dialog-message">
        <div>恭喜您，报名成功。请在活动开始后</div>
        <div>前往活动地点完成签到并参加活动！</div>
      </div>
      <div class="dialog-footer" @click="onSuccessConfirm">
        知道了
      </div>
    </van-dialog>

    <!-- 错误提示弹窗 - 非行内客户 -->
    <van-dialog
      v-model="showNonCustomerDialog"
      title=""
      class="customer-error-dialog"
      :show-confirm-button="false"
      :show-cancel-button="false"
    >
      <div class="customer-dialog-content">
        <div class="dialog-title">报名失败</div>
        <div class="dialog-message">
          <div>您好！您尚未开通南京银行账户。请联</div>
          <div>系您的客户经理协助开通账户，即可参</div>
          <div>与活动。客户经理微信：</div>
        </div>
        <div class="manager-card">
          <img src="@/images/activity/kfBg.png" alt="客户经理信息" />
        </div>
        <div class="qr-tip">扫描二维码，添加我的企业微信</div>
        <div class="dialog-footer" @click="onErrorDialogConfirm">
          知道了
        </div>
      </div>
    </van-dialog>

    <!-- 错误提示弹窗 - 客户等级不足 -->
    <van-dialog
      v-model="showLevelDialog"
      title=""
      class="customer-error-dialog"
      :show-confirm-button="false"
      :show-cancel-button="false"
    >
      <div class="customer-dialog-content">
        <div class="dialog-title">报名失败</div>
        <div class="dialog-message">
          <div>您好！您的账户暂不符合本次活动参与</div>
          <div>条件。如需了解详情，请联系您的客户</div>
          <div>经理：</div>
        </div>
        <div class="manager-card">
          <img src="@/images/activity/kfBg.png" alt="客户经理信息" />
        </div>
        <div class="qr-tip">扫描二维码，添加我的企业微信</div>
        <div class="dialog-footer" @click="onErrorDialogConfirm">
          知道了
        </div>
      </div>
    </van-dialog>

    <!-- 错误提示弹窗 - 活动已结束 -->
    <van-dialog
      v-model="showTimeEndDialog"
      title=""
      class="simple-error-dialog"
      :show-confirm-button="false"
      :show-cancel-button="false"
    >
      <div class="simple-dialog-content">
        <div class="simple-title">报名失败</div>
        <div class="simple-message">活动已结束报名，请参加其它活动吧</div>
        <div class="simple-footer" @click="onErrorDialogConfirm">
          知道了
        </div>
      </div>
    </van-dialog>

    <!-- 错误提示弹窗 - 报名人数已满 -->
    <van-dialog
      v-model="showFullDialog"
      title=""
      class="simple-error-dialog"
      :show-confirm-button="false"
      :show-cancel-button="false"
    >
      <div class="simple-dialog-content">
        <div class="simple-title">报名失败</div>
        <div class="simple-message">活动太火爆了，请参加其它活动吧</div>
        <div class="simple-footer" @click="onErrorDialogConfirm">
          知道了
        </div>
      </div>
    </van-dialog>

  <!-- 自定义身份证数字键盘（含 X） -->
  <van-number-keyboard
    :show="showIdKeyboard"
    :extra-key="'X'"
    :show-delete-key="true"
    :hide-on-click-outside="true"
    close-button-text="完成"
    @input="onIdKeyboardInput"
    @delete="onIdKeyboardDelete"
    @close="onIdKeyboardClose"
  />
  </div>
</template>

<script>
import Vue from 'vue';
import {
  NavBar,
  Icon,
  Button,
  Field,
  Stepper,
  Popup,
  Picker,
  Dialog,
  Toast,
  ActionSheet,
  NumberKeyboard
} from 'vant';
import util from '@/util/util';
import common from '@/util/common';

Vue.use(NavBar)
  .use(Icon)
  .use(Button)
  .use(Field)
  .use(Stepper)
  .use(Popup)
  .use(Picker)
  .use(Dialog)
  .use(Toast)
  .use(ActionSheet)
  .use(NumberKeyboard);

export default {
  name: 'ActivityRegistration',
  data() {
    return {
      activity: {},
      isActivityInfoExpanded: false, // 默认折叠
      submitting: false,
      showSuccessDialog: false,
      showNonCustomerDialog: false,
      showLevelDialog: false,
      showTimeEndDialog: false,
      showFullDialog: false,

      // 报名成功后的返回数据
      registrationResult: null,

      // 自定义身份证键盘
      showIdKeyboard: false,
      currentIdField: null,
      
      // 表单数据
      formData: {
        name: '',
        phone: '', // 默认取登录人员手机号，不支持修改
        cardType: '',
        idCard: '',
        age: '',
        gender: '',
        height: '',
        weight: '',
        education: '',
        adultCount: 0,
        childCount: 0,
        customFields: {}
      },
      
      // 字段错误信息
      fieldErrors: {},
      
      // 选择器相关
      showCustomFieldPickerDialog: false,
      currentCustomField: null,
      
      // 选项数据
      cardTypeOptions: ['身份证', '护照', '港澳通行证', '台胞证'],
      genderOptions: ['男', '女', '其他'],
      educationOptions: ['小学', '初中', '高中', '大专', '本科', '硕士', '博士'],
      
      // 自定义字段
      customFields: [],
      maxFamilyMembers: 5, // 携带人数上限
      
      // 字段必填配置（从后端获取）
      fieldRequiredConfig: {
        name: true,        // 姓名必填
        phone: true,       // 手机号必填（默认填充，不支持修改）
        cardType: false,   // 证件类型
        idCard: true,      // 身份证号码必填
        age: true,         // 年龄必填
        gender: true,      // 性别必填
        height: false,     // 身高
        weight: false,     // 体重
        education: false,  // 学历
        familyMembers: false // 携带亲属
      }
    };
  },
  
  computed: {
    currentCustomFieldOptions() {
      if (!this.currentCustomField) return [];
      return this.currentCustomField.options || [];
    },

    // 所有字段（包括预设字段和自定义字段）
    allFields() {
      return this.customFields;
    },

  },
  
  mounted() {
    this.loadActivityData();
    this.loadRegistrationConfig();
    this.loadUserInfo();
  },
  
  methods: {
    // 返回上一页
    goBack() {
      util.back('#/myActivities');
    },
    
    // 加载用户信息
    loadUserInfo() {
      try {
        // 从localStorage获取用户信息
        const userInfoStr = localStorage.getItem('userInfo');
        if (userInfoStr) {
          const userInfo = JSON.parse(userInfoStr);
          // 只预填充手机号到表单，从blurMobile字段获取
          if (userInfo.blurMobile) {
            this.formData.phone = userInfo.blurMobile;
          }
        }
      } catch (error) {
        console.error('加载用户信息失败:', error);
      }
    },
    
    // 加载活动数据
    async loadActivityData() {
      try {
        const activityId = this.$route.params.id;
        const response = await this.$api.getActivityDetail(activityId);
        
        if (response) {
          
          // 安全地获取数据，处理不同的数据结构
          const data =  response.data ;
          
          if (data) {
            this.activity = {
              id: activityId,
              actTitle: data.actTitle|| '活动名称',
              actTime: data.actTime|| '时间待定',
              locationString: data.locationString || '地址待定',
              actNotice: data.actNotice|| '暂无须知',
              actDesc: data.actDesc || ''
            };

            // 处理活动用户字段配置
            if (data.activityUserDTOList && Array.isArray(data.activityUserDTOList)) {
              this.processActivityUserFields(data.activityUserDTOList);
            }
          } else {
            console.warn('接口返回数据为空');
          }
        } else {
          console.warn('接口返回为空');
        }
      } catch (error) {
        console.error('加载活动数据失败:', error);
      }
    },
    
    // 处理活动用户字段配置
    processActivityUserFields(activityUserDTOList) {
      try {
        const allFields = [];

        activityUserDTOList.forEach(field => {
          const customField = this.convertToCustomField(field);
          if (customField) {
            // 将 mobile 字段映射到 phone 字段
            if (customField.id === 'mobile') {
              customField.id = 'phone';
            }

            // 为携带亲属字段初始化步进器为0
            if (customField.id === 'relative' || customField.label.includes('亲属') || customField.label.includes('携带')) {
              this.$set(this.formData, customField.id + '_adult', 0);
              this.$set(this.formData, customField.id + '_child', 0);
              this.$set(this.formData, customField.id, '');
            }

            allFields.push(customField);
          }
        });

        this.customFields = allFields;

        console.log('所有字段已更新:', this.customFields);
        console.log('原始接口数据:', activityUserDTOList);
      } catch (error) {
        console.error('处理活动用户字段配置失败:', error);
      }
    },


    // 将接口字段转换为自定义字段格式
    convertToCustomField(field) {
      try {
        const fieldType = this.getFieldTypeFromNumber(field.type);

        return {
          id: field.key,
          label: field.title,
          type: fieldType,
          required: field.required === true,
          options: field.text ? field.text.split(/[,，]/) : null,
          result: field.result || null,
          human: field.human !== null && field.human !== undefined ? field.human : null,
          child: field.child !== null && field.child !== undefined ? field.child : null,
          // 标记是否为身份证字段（根据后端 key 或标题判断）
          isIdCard: (field.key === 'idCard') || (typeof field.title === 'string' && field.title.includes('身份证'))
        };
      } catch (error) {
        console.error('转换自定义字段失败:', error);
        return null;
      }
    },

    // 根据数字类型转换为字段类型
    getFieldTypeFromNumber(typeNumber) {
      const typeMap = {
        0: 'text',      // 文本
        1: 'textarea',  // 多行文本框
        2: 'select'     // 选择
      };
      return typeMap[typeNumber] || 'text';
    },

    // 加载报名配置
    async loadRegistrationConfig() {
      try {
        // 报名配置现在通过activityUserDTOList处理，这里保留空实现
        // 如果需要额外的配置，可以在这里添加
      } catch (error) {
        console.error('加载报名配置失败:', error);
      }
    },
    
    // 加载字段必填配置
    async loadFieldRequiredConfig() {
      try {
        // 字段必填配置现在通过activityUserDTOList在loadActivityData中处理
        // 这里保留空实现，如果需要额外的配置可以在这里添加
        console.log('字段必填配置已通过activityUserDTOList处理');
      } catch (error) {
        console.error('加载字段必填配置失败:', error);
      }
    },
    
    // 切换活动信息展开/折叠
    toggleActivityInfo() {
      this.isActivityInfoExpanded = !this.isActivityInfoExpanded;
    },
    
    
    // 显示字段选择器
    showFieldPicker(field) {
      // 确保字段有选项数据
      if (!field || !field.options || field.options.length === 0) {
        Toast('选项数据加载失败');
        return;
      }

      this.currentCustomField = field;
      this.showCustomFieldPickerDialog = true;
    },

    


    // 携带亲属步进器变化处理
    onRelativeStepperChange(field) {
      const adultCount = parseInt(this.formData[field.id + '_adult']) || 0;
      const childCount = parseInt(this.formData[field.id + '_child']) || 0;
      const maxAdult = field.human || this.maxFamilyMembers;
      const maxChild = field.child || this.maxFamilyMembers;

      // 检查成人数是否超过限制
      if (adultCount > maxAdult) {
        this.$toast(`成人人数不能超过${maxAdult}人`);
        this.$set(this.formData, field.id + '_adult', maxAdult);
      }

      // 检查儿童数是否超过限制
      if (childCount > maxChild) {
        this.$toast(`儿童人数不能超过${maxChild}人`);
        this.$set(this.formData, field.id + '_child', maxChild);
      }

      // 更新字段的结果值，格式化为 "大X人，小X人" 的形式
      const finalAdultCount = parseInt(this.formData[field.id + '_adult']) || 0;
      const finalChildCount = parseInt(this.formData[field.id + '_child']) || 0;

      let resultText = '';
      if (finalAdultCount > 0 && finalChildCount > 0) {
        resultText = `大${finalAdultCount}人，小${finalChildCount}人`;
      } else if (finalAdultCount > 0) {
        resultText = `大${finalAdultCount}人`;
      } else if (finalChildCount > 0) {
        resultText = `小${finalChildCount}人`;
      } else {
        resultText = '';
      }

      this.$set(this.formData, field.id, resultText);

      // 更新 human 和 child 值用于提交
      if (field.human !== null && field.human !== undefined) {
        this.formData.adultCount = finalAdultCount;
        this.formData.childCount = finalChildCount;
      }

      this.validateField(field.id);
    },

    // 步进器变化处理
    onStepperChange(fieldId) {
      // 确保步进器值不超过上限
      const value = this.formData[fieldId];
      if (value > this.maxFamilyMembers) {
        this.$set(this.formData, fieldId, this.maxFamilyMembers);
        this.$toast(`人数不能超过${this.maxFamilyMembers}人`);
      }

      // 如果是成人和儿童数，需要验证总数
      if (fieldId === 'adultCount' || fieldId === 'childCount') {
        this.validateFamilyMembers();
      }

      // 验证当前字段
      this.validateField(fieldId);
    },

    // 限制小数位数（允许输入过程，在失去焦点时格式化）
    limitDecimalPlaces(fieldId, decimalPlaces) {
      const value = this.formData[fieldId];

      // 如果是空值或者正在输入小数点，不进行处理
      if (value === null || value === undefined || value === '' || value === '.') {
        return;
      }

      // 如果是以小数点结尾（用户正在输入小数），暂时不处理
      if (typeof value === 'string' && value.endsWith('.')) {
        return;
      }

      const numValue = parseFloat(value);
      if (!isNaN(numValue)) {
        // 检查小数位数，只有超过限制时才处理
        const valueStr = value.toString();
        const decimalIndex = valueStr.indexOf('.');
        if (decimalIndex !== -1 && valueStr.length - decimalIndex - 1 > decimalPlaces) {
          const roundedValue = Math.round(numValue * Math.pow(10, decimalPlaces)) / Math.pow(10, decimalPlaces);
          this.$set(this.formData, fieldId, roundedValue.toString());
        }
      }
    },

    // 格式化小数位数（失去焦点时调用）
    formatDecimalPlaces(fieldId, decimalPlaces) {
      const value = this.formData[fieldId];
      if (value !== null && value !== undefined && value !== '') {
        const numValue = parseFloat(value);
        if (!isNaN(numValue)) {
          const roundedValue = Math.round(numValue * Math.pow(10, decimalPlaces)) / Math.pow(10, decimalPlaces);
          this.$set(this.formData, fieldId, roundedValue.toString());
        }
      }
    },

    // 限制为整数输入
    limitToInteger(fieldId) {
      const value = this.formData[fieldId];
      if (value !== null && value !== undefined && value !== '') {
        const intValue = parseInt(value, 10);
        if (!isNaN(intValue)) {
          this.$set(this.formData, fieldId, intValue.toString());
        } else {
          this.$set(this.formData, fieldId, '');
        }
      }
    },

    // 身份证号输入处理
    onIdCardInput(fieldId) {
      let value = this.formData[fieldId];
      if (value) {
        // 只允许数字和X/x
        value = value.replace(/[^0-9Xx]/g, '');
        // 限制长度为18位
        if (value.length > 18) {
          value = value.substring(0, 18);
        }
        // 将小写x转换为大写X
        value = value.replace(/x/g, 'X');
        this.$set(this.formData, fieldId, value);
      }
    },

    // 打开身份证定制键盘
    openIdKeyboard(fieldId) {
      if (this.showIdKeyboard && this.currentIdField === fieldId) {
        return;
      }
      // 确保先关闭之前的键盘状态
      this.closeIdKeyboard();
      this.currentIdField = fieldId;
      this.showIdKeyboard = true;
    },

    // 关闭身份证键盘
    closeIdKeyboard() {
      this.showIdKeyboard = false;
      this.currentIdField = null;
    },

    // 键盘输入
    onIdKeyboardInput(key) {
      const fieldId = this.currentIdField || 'idCard';
      let value = String(this.formData[fieldId] || '');

      // 只接受数字和X
      if (!/^[0-9Xx]$/.test(key)) {
        return;
      }

      if (value.length >= 18) {
        return;
      }

      // 始终转为大写 X
      if (key === 'x') key = 'X';
      value += key;
      this.$set(this.formData, fieldId, value);
    },

    // 键盘删除
    onIdKeyboardDelete() {
      const fieldId = this.currentIdField || 'idCard';
      const value = String(this.formData[fieldId] || '');
      if (!value) return;
      this.$set(this.formData, fieldId, value.slice(0, -1));
    },

    // 键盘关闭
    onIdKeyboardClose() {
      const fieldId = this.currentIdField || 'idCard';
      this.validateField(fieldId);
      // 使用统一的关闭方法
      this.closeIdKeyboard();
    },

    // 插入X到身份证号（不再使用UI按钮，保留方法以防外部调用）
    insertX() {
      const fieldId = this.currentIdField || 'idCard';
      const currentValue = String(this.formData[fieldId] || '');
      if (currentValue.length < 18) {
        this.$set(this.formData, fieldId, currentValue + 'X');
      }
    },

    // 验证身份证号码
    validateIdCard(idCard) {
      if (idCard === null || idCard === undefined) {
        return false;
      }

      // 统一转为字符串处理，去除首尾空格
      idCard = String(idCard).trim();

      if (idCard.length !== 18) {
        return false;
      }

      // 基础格式校验：前17位必须是数字，第18位可以是数字或X
      if (!/^[1-9]\d{16}[\dXx]$/.test(idCard)) {
        return false;
      }

      // 省份代码校验（前两位）
      const provinceCode = parseInt(idCard.substring(0, 2));
      const validProvinceCodes = [11, 12, 13, 14, 15, 21, 22, 23, 31, 32, 33, 34, 35, 36, 37, 41, 42, 43, 44, 45, 46, 50, 51, 52, 53, 54, 61, 62, 63, 64, 65, 71, 81, 82];
      if (!validProvinceCodes.includes(provinceCode)) {
        return false;
      }

      // 出生日期校验（第7-14位）
      const year = parseInt(idCard.substring(6, 10));
      const month = parseInt(idCard.substring(10, 12));
      const day = parseInt(idCard.substring(12, 14));

      const now = new Date();
      const currentYear = now.getFullYear();
      if (year < 1900 || year > currentYear) {
        return false;
      }
      if (month < 1 || month > 12) {
        return false;
      }
      if (day < 1 || day > 31) {
        return false;
      }

      // 月份天数校验
      const daysInMonth = new Date(year, month, 0).getDate();
      if (day > daysInMonth) {
        return false;
      }

      // 真实日期与年龄范围校验：生日不能晚于今天，且年龄不超过120岁
      const birthDate = new Date(year, month - 1, day);
      // birthDate 有效性已由上面天数校验保障，这里再做时间线校验
      if (birthDate.getTime() > now.getTime()) {
        return false;
      }
      // 计算年龄
      let age = currentYear - year;
      const hasHadBirthdayThisYear = (now.getMonth() + 1 > month) || ((now.getMonth() + 1 === month) && now.getDate() >= day);
      if (!hasHadBirthdayThisYear) {
        age -= 1;
      }
      if (age < 0 || age > 120) {
        return false;
      }

      // 校验码验证（第18位）
      const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

      let sum = 0;
      for (let i = 0; i < 17; i++) {
        sum += parseInt(idCard.charAt(i)) * weights[i];
      }

      const checkCode = checkCodes[sum % 11];
      const lastChar = idCard.charAt(17).toUpperCase();

      return checkCode === lastChar;
    },

    // 验证单个字段
    validateField(fieldName) {
      const value = this.formData[fieldName];
      const field = this.customFields.find(f => f.id === fieldName);
      let error = '';

      // 先检查必填项
      if (field && field.required && (!value || (value.trim && !value.trim()))) {
        error = `请填写${field.label}`;
      } else if (value && value.trim && value.trim()) {
        // 有值的情况下进行格式验证
        if (field && field.isIdCard) {
          const trimmedValue = String(value).trim();
          if (trimmedValue.length === 18) {
            if (!this.validateIdCard(value)) {
              error = '身份证号码不正确，请检查';
            }
          } else if (trimmedValue.length > 0) {
            error = '身份证号码应为18位';
          }
        } else {
          switch (fieldName) {
          case 'idCard':
            const trimmedValue = String(value).trim();
            if (trimmedValue.length === 18) {
              if (!this.validateIdCard(value)) {
                error = '身份证号码不正确，请检查';
              }
            } else if (trimmedValue.length > 0) {
              error = '身份证号码应为18位';
            }
            break;
          case 'age':
            const ageNum = parseInt(value);
            if (isNaN(ageNum) || ageNum < 1 || ageNum > 120) {
              error = '年龄应在1-120岁之间';
            }
            break;
          case 'height':
            const heightNum = parseFloat(value);
            if (isNaN(heightNum) || heightNum < 50 || heightNum > 250) {
              error = '身高应在50-250cm之间';
            }
            break;
          case 'weight':
            const weightNum = parseFloat(value);
            if (isNaN(weightNum) || weightNum < 10 || weightNum > 300) {
              error = '体重应在10-300kg之间';
            }
            break;
          }
        }
      }

      this.$set(this.fieldErrors, fieldName, error);
      return !error;
    },
    
    
    // 验证携带亲属
    validateFamilyMembers() {
      const adultCount = parseInt(this.formData.adultCount) || 0;
      const childCount = parseInt(this.formData.childCount) || 0;
      const total = adultCount + childCount;
      let error = '';

      if (total > this.maxFamilyMembers) {
        error = `携带人数不能超过${this.maxFamilyMembers}人`;

        // 自动调整人数，优先调整儿童数
        if (childCount > 0) {
          const maxChild = Math.max(0, this.maxFamilyMembers - adultCount);
          this.$set(this.formData, 'childCount', maxChild);
        }

        // 如果还是超出，再调整成人数
        const newTotal = parseInt(this.formData.adultCount) + parseInt(this.formData.childCount);
        if (newTotal > this.maxFamilyMembers) {
          this.$set(this.formData, 'adultCount', Math.max(0, this.maxFamilyMembers - parseInt(this.formData.childCount)));
        }
      }

      this.$set(this.fieldErrors, 'familyMembers', error);
      return !error;
    },
    

    
    // 显示自定义字段选择器
    showCustomFieldPicker(field) {
      // 确保字段有选项数据
      if (!field || !field.options || field.options.length === 0) {
        Toast('选项数据加载失败');
        return;
      }

      this.currentCustomField = field;
      this.showCustomFieldPickerDialog = true;
    },
    
    onCustomFieldConfirm(value, index) {
      try {
        // 处理不同的参数格式
        let selectedValue = value;

        // 如果 value 是数组（多列选择器的情况）
        if (Array.isArray(value) && value.length > 0) {
          selectedValue = value[0];
        }

        // 如果 selectedValue 是对象，提取实际值
        if (typeof selectedValue === 'object' && selectedValue !== null) {
          selectedValue = selectedValue.value || selectedValue.text || selectedValue.name || selectedValue;
        }

        if (this.currentCustomField && selectedValue !== undefined && selectedValue !== null) {
          this.$set(this.formData, this.currentCustomField.id, selectedValue);
          this.validateField(this.currentCustomField.id);
        }
      } catch (error) {
        console.error('处理选择器确认事件时出错:', error);
      } finally {
        // 确保弹框关闭
        this.showCustomFieldPickerDialog = false;
        this.currentCustomField = null;
      }
    },
    

    onCustomFieldCancel() {
      this.showCustomFieldPickerDialog = false;
      this.currentCustomField = null;
    },
    
    // 提交报名
    async submitRegistration() {
      // 防止重复提交
      if (this.submitting) return;

      // 验证所有字段
      let hasError = false;
      let firstErrorMessage = '';

      this.customFields.forEach(field => {
        if (!this.validateField(field.id)) {
          hasError = true;
          // 记录第一个错误信息
          if (!firstErrorMessage && this.fieldErrors[field.id]) {
            firstErrorMessage = this.fieldErrors[field.id];
          }
        }
      });

      if (hasError) {
        // 显示具体的错误信息，如果没有具体错误信息则显示通用提示
        Toast(firstErrorMessage || '请填写带*的项目');
        return;
      }
      
      this.submitting = true;
      
      try {
        // 构建提交数据
        // 找到身份证字段（支持 title 为“身份证” 或 key 为 idCard）
        const dynamicIdCardField = this.customFields.find(f => f.isIdCard);

        const submitData = {
          id: this.activity.id,                    // 活动Id
          name: this.formData.name || '',          // 姓名
          phone: this.formData.phone || '',        // 手机号
          cardType: this.formData.cardType || '',  // 证件类型
          idCard: dynamicIdCardField ? (this.formData[dynamicIdCardField.id] || '') : (this.formData.idCard || ''),      // 身份证号
          gender: this.formData.gender || '',      // 性别
          age: this.formData.age || '',            // 年龄
          human: this.formData.adultCount || 0,    // 携带成人数
          child: this.formData.childCount || 0,    // 携带儿童数
          high: this.formData.height || '',        // 身高
          weight: this.formData.weight || '',      // 体重
          educate: this.formData.education || '',  // 学历
          community: '',                           // 社区
          address: '',                             // 地址
          selfAdds: this.buildSelfAdds()           // 自增项目
        };
        
        // 调试：打印提交的数据
        console.log('提交的报名数据:', submitData);
        console.log('selfAdds 数据:', submitData.selfAdds);
        
        // 调用报名接口
        const response = await this.$api.registerActivity(submitData);

        console.log('报名接口返回:', response);
        
        if (response && response.code === 200) {
          // 保存报名成功后返回的数据，包含新的ID
          this.registrationResult = response.data;
          this.showSuccessDialog = true;
        } else {
          // 根据错误消息显示不同的弹窗
          this.handleRegistrationError(response);
        }
      } catch (error) {
        console.error('报名失败:', error);
        // 在 catch 块中处理被 reject 的错误消息
        const errorMsg = error.message || error.toString() || error;
        this.handleRegistrationError({ msg: errorMsg });
      } finally {
        this.submitting = false;
      }
    },
    
    // 构建自增项目数据
    buildSelfAdds() {
      const selfAdds = [];

      // 遍历所有字段，构建自增项目
      this.customFields.forEach(field => {
        const value = this.formData[field.id];

        if (field.id === 'relative' || field.label.includes('亲属') || field.label.includes('携带')) {
          // 携带亲属字段特殊处理
          const adultCount = parseInt(this.formData[field.id + '_adult']) || 0;
          const childCount = parseInt(this.formData[field.id + '_child']) || 0;

          // 生成显示文本
          let resultText = '';
          if (adultCount > 0 && childCount > 0) {
            resultText = `成人${adultCount}人，儿童${childCount}人`;
          } else if (adultCount > 0) {
            resultText = `成人${adultCount}人`;
          } else if (childCount > 0) {
            resultText = `儿童${childCount}人`;
          }

          selfAdds.push({
            title: field.label,
            result: resultText,
            key: field.id,
            type: this.getFieldType(field.type),
            required: field.required || false,
            text: field.options ? field.options.join(',') : null,
            human: adultCount,
            child: childCount
          });
        } else if (value && value.trim && value.trim()) {
          // 普通字段
          selfAdds.push({
            title: field.label,
            result: value,
            key: field.id,
            type: this.getFieldType(field.type),
            required: field.required || false,
            text: field.options ? field.options.join(',') : null,
            human: null,
            child: null
          });
        }
      });

      return selfAdds;
    },
    
    // 获取字段类型数字
    getFieldType(type) {
      const typeMap = {
        'text': 0,      // 文本
        'textarea': 0,  // 文本域
        'select': 2,    // 选择
        'number': 1     // 数字
      };
      return typeMap[type] || 0;
    },
    
    // 处理报名错误
    handleRegistrationError(response) {
      const msg = response?.msg || '';
      console.log('错误消息:', msg);

      if (msg.includes('非行内客户') || msg.includes('不可报名')) {
        this.showNonCustomerDialog = true;
      } else if (msg.includes('报名资格不满足') || msg.includes('客户等级')) {
        this.showLevelDialog = true;
      } else if (msg.includes('活动报名时间已结束') || msg.includes('时间已结束')) {
        this.showTimeEndDialog = true;
      } else if (msg.includes('报名人数已满') || msg.includes('人数已满')) {
        this.showFullDialog = true;
      } else {
        // 其他错误使用Toast显示
        Toast(msg || '报名失败，请重试');
      }
    },

    // 成功确认
    onSuccessConfirm() {
      this.showSuccessDialog = false;
      // 报名成功后跳转到报名详情页面，使用返回的新ID
      if (this.registrationResult && this.registrationResult.registerId) {
        const detailUrl = `#/activity/${this.registrationResult.registerId}/registration/detail`;
        util.back(detailUrl);
      } else {
        util.back('#/myActivities');
      }
    },

    // 错误弹窗确认
    onErrorDialogConfirm() {
      this.showNonCustomerDialog = false;
      this.showLevelDialog = false;
      this.showTimeEndDialog = false;
      this.showFullDialog = false;
      this.goBack();
      }
    
  }
};
</script>

<style lang="less" scoped>
.registration-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

.activity-info-section,
.registration-form-section {
  background: white;
  margin: 8px 16px;
  border-radius: 6px;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 16px;
  position: relative;
  
  .header-left {
    display: flex;
    align-items: center;
    width: 100%;
    
    .orange-bar {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4.5px;
      height: 15px;
      background: #C28D4B;
      border-radius: 2.25px;
      margin-right: 3px;
    }
    
    .section-title {
      font-family: 'PingFang SC Semibold', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
      font-weight: 600;
      font-size: 16px;
      color: #373737;
      margin-left: 3px;
      text-align: left;
    }
  }
}

.activity-info-content {
  padding: 0 16px 16px;
  
  .info-item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    
    .label {
      font-family: 'PingFang SC', 'PingFang SC Medium', 'Microsoft YaHei', Arial, sans-serif;
      min-width: 80px;
      font-size: 14px;
      color: #373737;
      flex-shrink: 0;
    }
    
    .value {
      flex: 1;
      font-size: 14px;
      color: #333;
      word-break: break-all;
      line-height: 1.4;
      text-align: right;
    }
  }
  
  .expand-trigger {
    display: flex;
    justify-content: center;
    padding: 12px 0;
    border-top: 1px solid #f0f0f0;
    cursor: pointer;
    
    .expand-icon {
      color: #999;
      font-size: 16px;
    }
  }
}

.registration-form {
  padding: 3px;
  
  .form-group {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .unit {
    color: #999;
    font-size: 14px;
  }
  
  .stepper-group {
    display: flex;
    gap: 20px;
    
    .stepper-item {
      display: flex;
      align-items: center;
      gap: 4px;
      
      .stepper-label {
        font-family: 'PingFang SC', 'PingFang SC Medium', 'Microsoft YaHei', Arial, sans-serif;
        font-size: 14px;
        color: #373737;
        min-width: 15px;
      }
    }
  }
  
  // 修改步进器内部布局，让输入框和标签在同一行
  :deep(.van-stepper) {
    display: flex;
    align-items: center;
    
    .van-stepper__input {
      width: 25px;
      height: 20px;
      font-size: 11px;
      margin: 0 3px;
    }
    
    .van-stepper__button {
      width: 20px;
      height: 20px;
      font-size: 11px;
    }
    
    .van-stepper__minus,
    .van-stepper__plus {
      width: 20px;
      height: 20px;
      font-size: 11px;
    }
  }
  
  .custom-field {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .gender-options {
    display: flex;
    gap: 24px;

    .radio-option {
      display: flex;
      align-items: center;
      cursor: pointer;

      input[type="radio"] {
        margin-right: 8px;
        width: 14px;
        height: 14px;
        accent-color: #1989fa;
      }

      .radio-label {
        font-size: 13px;
        color: #333;
      }
    }
  }

  .idcard-input-wrapper {
    display: flex;
    align-items: center;
    width: 100%;

    .idcard-number-input {
      flex: 1;
      border: none;
      outline: none;
      font-size: 14px;
      text-align: right;
      padding: 0;
      background: transparent;

      &::placeholder {
        color: #c8c9cc;
      }
    }

    .id-card-x-btn {
      color: #1989fa;
      font-size: 16px;
      font-weight: 600;
      padding: 4px 8px;
      margin-left: 8px;
      cursor: pointer;
      user-select: none;
      flex-shrink: 0;

      &:active {
        opacity: 0.6;
      }
    }
  }
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: white;
  border-top: 1px solid #f0f0f0;
  
  .submit-button {
    width: 100%;
    height: 48px;
    background: linear-gradient(90deg, #C08A48 0%, #EDC391 100%);
    border: none;
    border-radius: 24px;
    font-size: 16px;
    font-weight: 500;
    color: white;
  }
}

:deep(.van-field) {
  margin-bottom: 16px;
}

:deep(.van-field__label) {
  color: #333;
  font-size: 14px;
  text-align: left;
  justify-content: flex-start;
  width: 80px;
  min-width: 80px;
  flex-shrink: 0;
  
  // 必填星号样式
  .required-star {
    color: #ff6b35;
    font-weight: bold;
    margin-left: 4px;
  }
}

:deep(.van-field__control) {
  font-size: 14px;
}

// 单行文本框右对齐
:deep(.van-field:not(.van-field--textarea) .van-field__control) {
  text-align: right;
}

// 多行文本框保持左对齐
:deep(.van-field--textarea .van-field__control) {
  text-align: left;
}

// 身份证输入框样式
:deep(.van-field .van-field__control[pattern="[0-9X]*"]) {
  text-transform: uppercase; // 自动转换为大写
  letter-spacing: 1px; // 增加字符间距，便于阅读
}

:deep(.van-field__error-message) {
  color: #ff6b35;
  font-size: 12px;
}

:deep(.van-stepper) {
  .van-stepper__input {
    width: 35px;
    height: 28px;
    font-size: 13px;
  }
  
  .van-stepper__button {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
}

// 禁用字段样式
.disabled-field {
  :deep(.van-field__control) {
    color: #999 !important;
    background-color: #f5f5f5 !important;
  }
}

// 成功弹窗样式
:deep(.success-dialog) {
  .van-dialog {
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #ffb3ba; // 粉色边框
    display: flex;
    flex-direction: column;
  }

  .van-dialog__header {
    padding: 20px 20px 10px;
    font-weight: 521;
    font-size: 16px !important;
    color: #333;
    text-align: center;
    flex-shrink: 0;
  }

  .van-dialog__content {
    padding: 10px 0px 0px;
    flex: 1;
  }

  .dialog-message {
    font-size: 15px;
    line-height: 1.5;
    color: #666;
    text-align: center;
    margin-bottom: 18px;
    div {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .dialog-footer {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px;
    color: #07c160;
    font-size: 16px;
    font-weight: 400;
    cursor: pointer;
    border-top: 1px solid #f0f0f0;
    box-sizing: border-box;
    flex-shrink: 0;
    margin: 0;

    &:hover {
      opacity: 0.8;
    }
  }
}

// 客户错误弹窗样式 - 按照设计图实现
:deep(.customer-error-dialog) {
  .van-dialog {
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-width: 320px;
    background: #f5f5f5;
  }

  .van-dialog__header {
    display: none; // 隐藏默认标题
  }

  .van-dialog__content {
    padding: 0;
    flex: 1;
  }

  .customer-dialog-content {
    background: #f5f5f5;
    padding: 30px 20px 20px;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .dialog-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
  }

  .dialog-message {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 25px;

    div {
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .manager-card {
    margin-bottom: 15px;
    width: 100%;
    max-width: 280px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    img {
      width: 100%;
      height: auto;
      display: block;
    }
  }

  .qr-tip {
    font-size: 12px;
    color: #666;
    margin-bottom: 25px;
  }

  .dialog-footer {
    background: #4CAF50;
    color: white;
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: normal;
    cursor: pointer;
    border: none;

    &:active {
      opacity: 0.8;
    }
  }
}

// 简单错误弹窗样式
:deep(.simple-error-dialog) {
  .van-dialog {
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-width: 300px;
    background: white;
  }

  .van-dialog__header {
    display: none; // 隐藏默认标题
  }

  .van-dialog__content {
    padding: 0;
    flex: 1;
  }

  .simple-dialog-content {
    padding: 30px 20px 20px;
    text-align: center;
  }

  .simple-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
  }

  .simple-message {
    font-size: 14px;
    line-height: 1.5;
    color: #666;
    margin-bottom: 30px;
  }

  .simple-footer {
    color: #4CAF50;
    font-size: 16px;
    font-weight: normal;
    cursor: pointer;

    &:active {
      opacity: 0.7;
    }
  }
}
</style>
