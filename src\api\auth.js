/*
 * @Author: Activity H5 Project
 * @Date: 2025-04-12
 * @Description: 登录认证相关API接口
 */

import request from '@/utils/request';

// 获取图片验证码
export const getImgCode = () => {
  return request({
    url: '/verification/sendImgValue',
    method: 'get',
    responseType: 'blob', 
    returnFullResponse: true // 返回完整响应对象，包含headers
  });
};

// 发送短信验证码
export const sendSmsCode = (phone, imgKey, imgValue) => {
  return request({
    url: '/app/verify/send',
    method: 'post',
    data: {
      mobile: phone,
      imgKey: imgKey,
      imgValue: imgValue
    }
  });
};

// 手机号短信登录
export const loginBySms = (phone, code) => {
  return request({
    url: '/appLogin',
    method: 'post',
    data: { mobile: phone, verification: code }
  });
};

// 获取当前登录用户信息
export const getUserInfo = () => {
  return request({
    url: '/app/userInfo',
    method: 'post'
  });
};

// 退出登录
export const logout = () => {
  return request({
    url: '/api/auth/logout',
    method: 'post'
  });
};

// 获取隐私协议内容
export const getPrivacyPolicy = () => {
  return request({
    url: '/app/privacy/get',
    method: 'get'
  });
};

// 获取用户协议内容
export const getUserAgreement = () => {
  return request({
    url: '/api/policy/agreement',
    method: 'get'
  });
};
