/*
 * @Descripttion:
 * @version:
 * @Author: ch<PERSON><PERSON>
 * @Date: 2019-04-02 14:29:27
 * @LastEditors: DESKTOP-TJTC9EU
 * @LastEditTime: 2023-08-03
 */
import { sha256 } from "js-sha256";
import cryptoJS from "crypto-js";
import { Toast } from "vant";
import JSEncrypt from 'jsencrypt';
import { encryptSwitch } from "@/api/my";


const common = {
    getENMode() {
        return new Promise((r, j) => {
            if (!window.sessionStorage.getItem('hyejEncryptSwitch')) {
                encryptSwitch().then((res) => {
                    if (res && res.code == 200 && res.data) {
                        window.sessionStorage.setItem('hyejEncryptSwitch', res.data.encryptSwitch ? 'yes' : 'no')
                        window.sessionStorage.setItem('rsaKey', res.data.publicKey || '')
                        console.log('获取加密模式正常')
                        r(true)
                    } else {
                        console.log('获取加密模式失败')
                        r(false)
                    }
                })
            } else {
                console.log('取缓存里的模式')
                r(true)
            }
        })
    },
    rsa(message) {
        // const JSEncrypt = require('jsencrypt').default;
        const jsEncrypt = new JSEncrypt();
        let key = window.sessionStorage.getItem('rsaKey')
        // 设置公钥
        jsEncrypt.setPublicKey(key);
        // 使用公钥加密
        const encrypted = jsEncrypt.encrypt(message);
        console.log('Encrypted:', encrypted);
        return encrypted;
        // console.log('Encrypted:', Base64.encode(encrypted));
    },

    getKEY(url) {
        let key = window.sessionStorage.getItem(url);

        // todo 是否需要清楚掉缓存
        window.sessionStorage.removeItem(url);
        return key;
    },
    removeKey(url) {
        window.sessionStorage.removeItem(url);
    },

    getAesKey(url) {
        let key = cryptoJS.lib.WordArray.random(16).toString(cryptoJS.enc.Hex)
        window.sessionStorage.setItem(url, key)
        return key;
        // CryptoJS.lib.WordArray.random(256 / 8).toString(CryptoJS.enc.Hex); // 使用Hex格式表示密钥
    },

    // 与后台交互的加密
    encryptAES(aesKey, content) {
        let sKey = cryptoJS.enc.Utf8.parse(aesKey);
        let sContent = cryptoJS.enc.Utf8.parse(content);
        let encrypted = cryptoJS.AES.encrypt(sContent, sKey, {
            mode: cryptoJS.mode.ECB,
            padding: cryptoJS.pad.Pkcs7,
        });
        // return encrypted.ciphertext.toString()
        return encrypted.toString();
    },
    // 后台交互解密
    dencryptAES(aesKey, content) {
        var key = cryptoJS.enc.Utf8.parse(aesKey);
        // var datahex = cryptoJS.enc.Hex.parse(content);
        // var dataBase64 = cryptoJS.enc.Base64.stringify(datahex);
        var decrypted = cryptoJS.AES.decrypt(content.toString(), key, {
            mode: cryptoJS.mode.ECB,
            padding: cryptoJS.pad.Pkcs7,
        });
        // console.log(decrypted,'00',dataBase64)
        return decrypted.toString(cryptoJS.enc.Utf8);
    },
    evn: "build", //dev开发模式，模拟登录，不加载cordova    build生产模式
    tips: {
        noResp: "网络连接超时，请稍后再试~",
        wait: "当前预约人数过多，请稍后再试~",
        hf: '<p style="line-height:1.3;">亲爱的，您操作太过频繁，<br>请稍后再试~</p>',
        noNet: "没有网络，请看下是否连上网络了~",
    },
    paperObj: {
        "01": "居民身份证",
        "03": "护照",
    },

    isInMini() {
        debugger
        return window.sessionStorage.getItem('inMini') == 'in'
    },


    // 退登
    // loginOut() {
    //     Toast({
    //         duration: 3000,
    //         forbidClick: true,
    //         message: "登录失效啦，即将重新登录~",
    //     });

    //     window.localStorage.removeItem("access_token")
    //     window.localStorage.removeItem("userInfo")
    //     // 保存当前路由路径，而不是完整URL
    //     const currentPath = window.location.hash || '#/';
    //     // window.sessionStorage.setItem("acURL", window.location.href)
    //     window.sessionStorage.setItem("acURL", currentPath)
    //     // window.location.href = "#/login"
    //     this.jump("#/login")
    // },





    sha256_(timeStamp, nonce) {

        let signingKey = "JlolWcxSD3fTdISQkEURIQ==";

        let salt_ = timeStamp % 10;
        let salt = nonce.substring(salt_);
        debugger;
        let stringSrc = signingKey + timeStamp + nonce + salt;
        return sha256(stringSrc);
    },

    getUrlParam: function (name) {
        var result = "";
        var url = window.location.href;
        // var url = (window.location.href);
        if (url.indexOf("?") > -1) {
            var param = url.substring(url.indexOf("?") + 1);
            // name += "=";
            let arr = param.split('&')
                .reduce(function (acc, param) {
                    const [key, value] = param.split('=');
                    acc[key] = decodeURIComponent(value);
                    return acc;
                }, {});
            /* if (param.indexOf(name) > -1) {
                var r = param.substr(param.indexOf(name) + name.length);
                if (r.indexOf("&") != -1) {
                    r = r.substring(0, r.indexOf("&"));
                }
                result = r;
            } */

            result = arr[name]

        }
        return result || "";
    },
    getUrlParam_(url, name) {
        var result = "";
        // var url = (window.location.href);
        /* if (url.indexOf("?") > -1) {
            var param = url.substring(url.indexOf("?") + 1);
            name += "=";
            if (param.indexOf(name) > -1) {
                var r = param.substr(param.indexOf(name) + name.length);
                if (r.indexOf("&") != -1) {
                    r = r.substring(0, r.indexOf("&"));
                }
                result = r;
            }
        }
        return result; */
        if (url.indexOf("?") > -1) {
            var param = url.substring(url.indexOf("?") + 1);
            let arr = param.split('&')
                .reduce(function (acc, param) {
                    const [key, value] = param.split('=');
                    acc[key] = decodeURIComponent(value);
                    return acc;
                }, {});
            result = arr[name]

        }
        return result;
    },

    updateUrlParam(url, name, value_) {
        debugger

        if (!url) {
            url = window.location.href;
        }

        if (url.indexOf("?") > -1) {
            var param = url.substring(url.indexOf("?") + 1);
            let arr = param.split('&');
            let obj = {};
            arr.forEach(element => {
                const [key, value] = element.split('=');
                if (key && value) {
                    if (key == name) {
                        obj[key] = value_;
                    } else {
                        obj[key] = decodeURIComponent(value);
                    }

                }
            });
            // obj[name] = value
            let str = "";
            for (let key in obj) {
                if (obj.hasOwnProperty(key)) {
                    str = str + key + "=" + obj[key] + "&";
                }
            }
            return url.substring(0, url.indexOf("?") + 1) + str;
        }

        return "";




    },

    toJSON(str) {
        return new Function("", "return " + str)();
    },


};

export default common;
