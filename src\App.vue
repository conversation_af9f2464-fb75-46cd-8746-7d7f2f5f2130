<template>
    <div id="app" :class="[uiStyle]">
        <router-view></router-view>
    </div>
</template>

<script>
export default {
    name: "App",
    data() {
        return {
            // 模式
            uiStyle: "",
            orgId: "",
        };
    },
    created() {
        // this.orgId = common.getUrlParam("orgId");
        // window.sessionStorage.setItem("orgId", this.orgId);
        // this.$store.dispatch('getOrgId',this.orgId)
        document.documentElement.style.setProperty(
            "--primary-color",
            this.$store.state.primaryColor
        );
    },
    methods: {},
};
</script>

<style>
#app {
    height: 100%;
    background: #f5f5f5;
    overflow-y: auto;
}
</style>
